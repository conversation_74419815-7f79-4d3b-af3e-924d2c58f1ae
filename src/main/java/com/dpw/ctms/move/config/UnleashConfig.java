package com.dpw.ctms.move.config;

import com.dpw.tmsutils.utils.TMSConfigUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

@Component
@Configuration
public class UnleashConfig {

    public JsonNode getMoveConfig() {
        ObjectMapper objectMapper = new ObjectMapper();
        ObjectNode contextNode = objectMapper.createObjectNode();
        contextNode.put("tenant", "CFR");
        JsonNode jsonNode = TMSConfigUtils.getFlagValue("move_static_api_data", contextNode, JsonNode.class);

        return jsonNode;
    }

}
